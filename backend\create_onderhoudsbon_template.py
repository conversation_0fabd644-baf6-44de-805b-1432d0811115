#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a DOCX template for the onderhoudsbon
"""

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def create_onderhoudsbon_template():
    """Create a DOCX template for the onderhoudsbon"""
    
    # Create a new document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Onderhoudsbon', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add basic info section
    doc.add_paragraph()
    
    # Create a table for basic information
    basic_info_table = doc.add_table(rows=4, cols=4)
    basic_info_table.style = 'Table Grid'
    
    # Fill basic info table
    cells = basic_info_table.rows[0].cells
    cells[0].text = 'Bonnummer:'
    cells[1].text = '{bonnummer}'
    cells[2].text = 'Telefoonnummer:'
    cells[3].text = '{telefoon}'
    
    cells = basic_info_table.rows[1].cells
    cells[0].text = 'Klantnummer:'
    cells[1].text = '{klantnummer}'
    cells[2].text = 'Contactpersoon:'
    cells[3].text = '{contactpersoon}'
    
    cells = basic_info_table.rows[2].cells
    cells[0].text = 'Bedrijf:'
    cells[1].text = '{bedrijf}'
    cells[2].text = 'Email:'
    cells[3].text = '{email}'
    
    cells = basic_info_table.rows[3].cells
    cells[0].text = 'Adres:'
    cells[1].text = '{adres}'
    cells[2].text = 'Type:'
    cells[3].text = '{type}'
    
    doc.add_paragraph()
    
    # Type installatie section
    doc.add_heading('Type installatie', level=2)
    type_para = doc.add_paragraph()
    type_para.add_run('{#inbraakmeldsysteem}☑{/inbraakmeldsysteem}{^inbraakmeldsysteem}☐{/inbraakmeldsysteem} Inbraakmeldsysteem    ')
    type_para.add_run('{#brandmeldsysteem}☑{/brandmeldsysteem}{^brandmeldsysteem}☐{/brandmeldsysteem} Brandmeldsysteem    ')
    type_para.add_run('{#cctv}☑{/cctv}{^cctv}☐{/cctv} CCTV')
    
    doc.add_paragraph()
    
    # Centrale / kiezer section
    doc.add_heading('Centrale / kiezer', level=2)
    centrale_table = doc.add_table(rows=6, cols=5)
    centrale_table.style = 'Table Grid'
    
    # Headers
    cells = centrale_table.rows[0].cells
    cells[0].text = 'Component'
    cells[1].text = 'Goed'
    cells[2].text = 'Fout'
    cells[3].text = 'NVT'
    cells[4].text = 'Opmerkingen'
    
    # Accu row
    cells = centrale_table.rows[1].cells
    cells[0].text = 'Accu'
    cells[1].text = '{#centrale_accu_goed}☑{/centrale_accu_goed}{^centrale_accu_goed}☐{/centrale_accu_goed}'
    cells[2].text = '{#centrale_accu_fout}☑{/centrale_accu_fout}{^centrale_accu_fout}☐{/centrale_accu_fout}'
    cells[3].text = '{#centrale_accu_nvt}☑{/centrale_accu_nvt}{^centrale_accu_nvt}☐{/centrale_accu_nvt}'
    cells[4].text = '{centrale_accu_opmerkingen}'
    
    # Voeding row
    cells = centrale_table.rows[2].cells
    cells[0].text = 'Voeding'
    cells[1].text = '{#centrale_voeding_goed}☑{/centrale_voeding_goed}{^centrale_voeding_goed}☐{/centrale_voeding_goed}'
    cells[2].text = '{#centrale_voeding_fout}☑{/centrale_voeding_fout}{^centrale_voeding_fout}☐{/centrale_voeding_fout}'
    cells[3].text = '{#centrale_voeding_nvt}☑{/centrale_voeding_nvt}{^centrale_voeding_nvt}☐{/centrale_voeding_nvt}'
    cells[4].text = '{centrale_voeding_opmerkingen}'
    
    # Lusspanning row
    cells = centrale_table.rows[3].cells
    cells[0].text = 'Lusspanning'
    cells[1].text = '{#centrale_lusspanning_goed}☑{/centrale_lusspanning_goed}{^centrale_lusspanning_goed}☐{/centrale_lusspanning_goed}'
    cells[2].text = '{#centrale_lusspanning_fout}☑{/centrale_lusspanning_fout}{^centrale_lusspanning_fout}☐{/centrale_lusspanning_fout}'
    cells[3].text = '{#centrale_lusspanning_nvt}☑{/centrale_lusspanning_nvt}{^centrale_lusspanning_nvt}☐{/centrale_lusspanning_nvt}'
    cells[4].text = '{centrale_lusspanning_opmerkingen}'
    
    # Uitlezing row
    cells = centrale_table.rows[4].cells
    cells[0].text = 'Uitlezing'
    cells[1].text = '{#centrale_uitlezing_goed}☑{/centrale_uitlezing_goed}{^centrale_uitlezing_goed}☐{/centrale_uitlezing_goed}'
    cells[2].text = '{#centrale_uitlezing_fout}☑{/centrale_uitlezing_fout}{^centrale_uitlezing_fout}☐{/centrale_uitlezing_fout}'
    cells[3].text = '{#centrale_uitlezing_nvt}☑{/centrale_uitlezing_nvt}{^centrale_uitlezing_nvt}☐{/centrale_uitlezing_nvt}'
    cells[4].text = '{centrale_uitlezing_opmerkingen}'
    
    # Algemene werking row
    cells = centrale_table.rows[5].cells
    cells[0].text = 'Algemene werking'
    cells[1].text = '{#centrale_algemeen_goed}☑{/centrale_algemeen_goed}{^centrale_algemeen_goed}☐{/centrale_algemeen_goed}'
    cells[2].text = '{#centrale_algemeen_fout}☑{/centrale_algemeen_fout}{^centrale_algemeen_fout}☐{/centrale_algemeen_fout}'
    cells[3].text = '{#centrale_algemeen_nvt}☑{/centrale_algemeen_nvt}{^centrale_algemeen_nvt}☐{/centrale_algemeen_nvt}'
    cells[4].text = '{centrale_algemeen_opmerkingen}'
    
    doc.add_paragraph()
    
    # Final section with signatures
    doc.add_heading('Akkoord', level=2)
    signature_table = doc.add_table(rows=4, cols=2)
    signature_table.style = 'Table Grid'
    
    cells = signature_table.rows[0].cells
    cells[0].text = 'Akkoord opdrachtgever'
    cells[1].text = 'Monteur'
    
    cells = signature_table.rows[1].cells
    cells[0].text = 'Naam: {klant_naam}'
    cells[1].text = 'Naam: {monteur_naam}'
    
    cells = signature_table.rows[2].cells
    cells[0].text = 'Datum: {datum}'
    cells[1].text = 'Begin/eindtijd: {begin_tijd} - {eind_tijd}'
    
    cells = signature_table.rows[3].cells
    cells[0].text = 'Handtekening: {klant_handtekening}'
    cells[1].text = 'Handtekening: {monteur_handtekening}'
    
    # Save the document
    output_path = os.path.join('sample_templates', 'onderhoudsbon_template.docx')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    doc.save(output_path)
    
    print(f"✓ Onderhoudsbon template created: {output_path}")
    return output_path

if __name__ == "__main__":
    create_onderhoudsbon_template()
