#!/usr/bin/env python3
"""
Check and verify the document template system is ready.
This script verifies database tables and dependencies are in place.
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def check_database_tables():
    """Check if required database tables exist."""
    print("Checking database tables...")
    
    try:
        from app import create_app, db
        from app.models.document_template import DocumentTemplate
        from app.models.document import Document
        from app.models.customer import Customer
        from app.models.user import User
        
        app = create_app()
        
        with app.app_context():
            # Check if tables exist by trying to query them
            try:
                DocumentTemplate.query.first()
                print("✓ document_templates table exists")
            except Exception as e:
                print(f"✗ document_templates table issue: {e}")
                return False
            
            try:
                Document.query.first()
                print("✓ documents table exists")
            except Exception as e:
                print(f"✗ documents table issue: {e}")
                return False
            
            try:
                Customer.query.first()
                print("✓ customers table exists")
            except Exception as e:
                print(f"✗ customers table issue: {e}")
                return False
            
            try:
                User.query.first()
                print("✓ users table exists")
            except Exception as e:
                print(f"✗ users table issue: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def check_dependencies():
    """Check if required Python packages are installed."""
    print("\nChecking Python dependencies...")
    
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'firebase_admin',
        'docx',
        'xml.etree.ElementTree',
        'zipfile',
        'marshmallow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'xml.etree.ElementTree':
                import xml.etree.ElementTree
            elif package == 'zipfile':
                import zipfile
            elif package == 'docx':
                import docx
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_firebase_config():
    """Check if Firebase is properly configured."""
    print("\nChecking Firebase configuration...")
    
    try:
        from app.utils.firebase import upload_file_to_storage
        print("✓ Firebase utilities available")
        
        # Check if Firebase is initialized (this will be done by the app)
        from firebase_admin import storage
        print("✓ Firebase admin SDK available")
        
        return True
        
    except Exception as e:
        print(f"✗ Firebase configuration issue: {e}")
        return False

def check_api_endpoints():
    """Check if new API endpoints are properly registered."""
    print("\nChecking API endpoints...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.app_context():
            # Get all registered routes
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append(rule.rule)
            
            # Check for new template endpoints
            required_endpoints = [
                '/api/document-templates',
                '/api/document-templates/<int:template_id>',
                '/api/document-templates/<int:template_id>/analyze',
                '/api/document-templates/<int:template_id>/generate'
            ]
            
            for endpoint in required_endpoints:
                # Check if endpoint pattern exists (allowing for variations)
                found = any(endpoint.replace('<int:template_id>', '<template_id>') in route or 
                          endpoint in route for route in routes)
                if found:
                    print(f"✓ {endpoint}")
                else:
                    print(f"✗ {endpoint} - NOT FOUND")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ API endpoint check failed: {e}")
        return False

def check_file_permissions():
    """Check if the application has proper file permissions."""
    print("\nChecking file permissions...")
    
    try:
        import tempfile
        
        # Test temporary file creation
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=True) as temp_file:
            temp_file.write(b'test content')
            print("✓ Temporary file creation works")
        
        return True
        
    except Exception as e:
        print(f"✗ File permission issue: {e}")
        return False

def main():
    """Run all checks."""
    print("=" * 60)
    print("DOCUMENT TEMPLATE SYSTEM READINESS CHECK")
    print("=" * 60)
    
    checks = [
        ("Database Tables", check_database_tables),
        ("Python Dependencies", check_dependencies),
        ("Firebase Configuration", check_firebase_config),
        ("API Endpoints", check_api_endpoints),
        ("File Permissions", check_file_permissions)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n{check_name}:")
        print("-" * len(check_name))
        if check_func():
            passed += 1
            print(f"✓ {check_name} - PASSED")
        else:
            print(f"✗ {check_name} - FAILED")
    
    print("\n" + "=" * 60)
    print(f"READINESS CHECK RESULTS: {passed}/{total} checks passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 System is ready! The document template system should work correctly.")
        print("\nNext steps:")
        print("1. Start the backend server: python run.py")
        print("2. Start the frontend: npm run dev")
        print("3. Go to Document Templates page to upload your first template")
        return 0
    else:
        print("❌ System not ready. Please fix the issues above before using the template system.")
        return 1

if __name__ == "__main__":
    exit(main())
