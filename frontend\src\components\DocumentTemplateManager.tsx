import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { getAllTemplates, createTemplate, deleteTemplate, getTemplateFileUrl } from '../services/documentTemplateService';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import { FaPlus, FaTrash, FaDownload, FaFileAlt, FaEye } from 'react-icons/fa';
import LoadingSpinner from './LoadingSpinner';
import DocumentTemplatePreview from './DocumentTemplatePreview';
import { ALLOWED_DOCUMENT_TYPES } from '../constants/documentTypes';

const DocumentTemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    name: '',
    document_type: '',
    description: '',
    file: null as File | null,
  });
  const [previewTemplate, setPreviewTemplate] = useState<DocumentTemplate | null>(null);
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const fetchedTemplates = await getAllTemplates();
      setTemplates(fetchedTemplates);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFormData({ ...formData, file: e.target.files[0] });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.document_type || !formData.file) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const data = new FormData();
      data.append('name', formData.name);
      data.append('document_type', formData.document_type);
      if (formData.description) {
        data.append('description', formData.description);
      }
      data.append('file', formData.file);

      await createTemplate(data);
      setFormData({
        name: '',
        document_type: '',
        description: '',
        file: null,
      });
      setShowAddForm(false);
      fetchTemplates();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create template');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = (templateId: number) => {
    showConfirmation({
      title: 'Delete Template',
      message: 'Are you sure you want to delete this template? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await deleteTemplate(templateId);
          setTemplates(templates.filter(template => template.id !== templateId));
          setError(null);
        } catch (err: any) {
          setError(err.response?.data?.error || 'Failed to delete template');
        }
      }
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  if (loading && templates.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-amspm-text">Document Templates</h2>
        {!showAddForm && user?.role === 'administrator' && (
          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary btn-sm flex items-center"
            disabled={submitting}
          >
            <FaPlus className="mr-2" /> Add Template
          </button>
        )}
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      {showAddForm && (
        <form onSubmit={handleSubmit} className="mb-8 bg-gray-50 dark:bg-dark-secondary p-4 rounded-md">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
                Template Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text"
                required
                disabled={submitting}
              />
            </div>
            <div>
              <label htmlFor="document_type" className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
                Document Type *
              </label>
              <select
                id="document_type"
                name="document_type"
                value={formData.document_type}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text"
                required
                disabled={submitting}
              >
                <option value="">Select Document Type</option>
                {ALLOWED_DOCUMENT_TYPES.map((type) => (
                  <option key={type} value={type}>
                    {type.replace(/_/g, ' ')}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text"
                rows={3}
                disabled={submitting}
              />
            </div>
            <div className="md:col-span-2">
              <label htmlFor="file" className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
                Template File (DOCX) *
              </label>
              <input
                type="file"
                id="file"
                name="file"
                onChange={handleFileChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text"
                accept=".docx,.doc"
                required
                disabled={submitting}
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Upload a DOCX file with form fields (checkboxes, text inputs) that can be filled in.
              </p>
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-4">
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="btn btn-outline btn-sm"
              disabled={submitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary btn-sm"
              disabled={submitting || !formData.name || !formData.document_type || !formData.file}
            >
              {submitting ? <LoadingSpinner size="sm" /> : 'Save Template'}
            </button>
          </div>
        </form>
      )}

      {templates.length === 0 ? (
        <p className="text-gray-500 dark:text-dark-text-light italic">No document templates available.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-dark-secondary">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                  Document Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                  Created By
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                  Created At
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
              {templates.map((template) => (
                <tr key={template.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                    <div className="flex items-center">
                      <FaFileAlt className="mr-2 text-amspm-primary dark:text-dark-accent" />
                      {template.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                    {template.document_type.replace(/_/g, ' ')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                    {template.created_by_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                    {formatDate(template.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setPreviewTemplate(template)}
                        className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                        title="Preview Template Fields"
                      >
                        <FaEye />
                      </button>
                      <a
                        href={getTemplateFileUrl(template.id)}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title="Download Template"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <FaDownload />
                      </a>
                      {user?.role === 'administrator' && (
                        <button
                          onClick={() => handleDelete(template.id)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete Template"
                        >
                          <FaTrash />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Preview Modal */}
      {previewTemplate && (
        <DocumentTemplatePreview
          template={previewTemplate}
          onClose={() => setPreviewTemplate(null)}
        />
      )}
    </div>
  );
};

export default DocumentTemplateManager;
