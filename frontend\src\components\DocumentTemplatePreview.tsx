import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aFileAlt, FaCheckSquare, FaTable } from 'react-icons/fa';
import TemplateService from '../services/templateService';

interface DocumentTemplatePreviewProps {
  template: DocumentTemplate;
  onClose: () => void;
}

interface TemplateAnalysis {
  template_id: number;
  template_name: string;
  document_type: string;
  fields: Array<{
    name: string;
    label: string;
    type: string;
    required: boolean;
  }>;
  checkboxes: Array<{
    name: string;
    label: string;
    type: string;
    required: boolean;
  }>;
  tables: Array<{
    index: number;
    has_fields: boolean;
    fields: Array<{
      name: string;
      label: string;
      row: number;
      cell: number;
      type: string;
    }>;
    structure: string;
  }>;
}

const DocumentTemplatePreview: React.FC<DocumentTemplatePreviewProps> = ({
  template,
  onClose
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<TemplateAnalysis | null>(null);

  useEffect(() => {
    loadTemplateAnalysis();
  }, [template.id]);

  const loadTemplateAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);

      const templateAnalysis = await TemplateService.analyzeTemplateFromBackend(template.id);
      setAnalysis(templateAnalysis);
    } catch (err: any) {
      setError(`Failed to analyze template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const getFieldTypeIcon = (type: string) => {
    switch (type) {
      case 'checkbox':
        return <FaCheckSquare className="text-blue-500" />;
      case 'date':
        return <FaFileAlt className="text-green-500" />;
      case 'email':
        return <FaFileAlt className="text-purple-500" />;
      case 'tel':
        return <FaFileAlt className="text-orange-500" />;
      case 'number':
        return <FaFileAlt className="text-red-500" />;
      case 'textarea':
        return <FaFileAlt className="text-yellow-500" />;
      default:
        return <FaFileAlt className="text-gray-500" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-dark-card rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-amspm-text flex items-center">
            <FaEye className="mr-2" /> Template Preview: {template.name}
          </h2>
          <button
            onClick={onClose}
            className="btn btn-outline btn-sm"
          >
            Close
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <FaSpinner className="animate-spin mr-2" />
              <span>Analyzing template...</span>
            </div>
          ) : error ? (
            <div className="text-red-500 text-center py-8">
              <p>{error}</p>
              <button
                onClick={loadTemplateAnalysis}
                className="btn btn-outline btn-sm mt-4"
              >
                Retry
              </button>
            </div>
          ) : analysis ? (
            <div className="space-y-6">
              {/* Template Info */}
              <div className="bg-gray-50 dark:bg-dark-secondary rounded-lg p-4">
                <h3 className="text-lg font-semibold text-amspm-text mb-2">Template Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Name:</span> {analysis.template_name}
                  </div>
                  <div>
                    <span className="font-medium">Document Type:</span> {analysis.document_type}
                  </div>
                </div>
              </div>

              {/* Text Fields */}
              {analysis.fields.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-amspm-text mb-4 flex items-center">
                    <FaFileAlt className="mr-2" /> Text Fields ({analysis.fields.length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {analysis.fields.map((field, index) => (
                      <div key={index} className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                        <div className="flex items-center mb-2">
                          {getFieldTypeIcon(field.type)}
                          <span className="ml-2 font-medium">{field.label}</span>
                          {field.required && (
                            <span className="ml-2 text-red-500 text-sm">*</span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <div>Field: <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">{field.name}</code></div>
                          <div>Type: <span className="capitalize">{field.type}</span></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Checkboxes */}
              {analysis.checkboxes.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-amspm-text mb-4 flex items-center">
                    <FaCheckSquare className="mr-2" /> Checkboxes ({analysis.checkboxes.length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {analysis.checkboxes.map((field, index) => (
                      <div key={index} className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                        <div className="flex items-center mb-2">
                          <FaCheckSquare className="text-blue-500 mr-2" />
                          <span className="font-medium">{field.label}</span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          <div>Field: <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">{field.name}</code></div>
                          <div>Type: Checkbox</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Tables */}
              {analysis.tables.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-amspm-text mb-4 flex items-center">
                    <FaTable className="mr-2" /> Tables with Fields ({analysis.tables.length})
                  </h3>
                  {analysis.tables.map((table, tableIndex) => (
                    <div key={tableIndex} className="bg-white dark:bg-dark-input border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                      <h4 className="font-semibold mb-3">Table {table.index + 1}</h4>
                      {table.fields.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {table.fields.map((field, fieldIndex) => (
                            <div key={fieldIndex} className="bg-gray-50 dark:bg-dark-secondary rounded p-2">
                              <div className="flex items-center mb-1">
                                {getFieldTypeIcon(field.type)}
                                <span className="ml-2 font-medium text-sm">{field.label}</span>
                              </div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                <div>Field: <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded">{field.name}</code></div>
                                <div>Position: Row {field.row + 1}, Cell {field.cell + 1}</div>
                                <div>Type: <span className="capitalize">{field.type}</span></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Summary */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Summary</h3>
                <div className="text-blue-700 dark:text-blue-300 text-sm">
                  <p>This template contains:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>{analysis.fields.length} text field{analysis.fields.length !== 1 ? 's' : ''}</li>
                    <li>{analysis.checkboxes.length} checkbox{analysis.checkboxes.length !== 1 ? 'es' : ''}</li>
                    <li>{analysis.tables.length} table{analysis.tables.length !== 1 ? 's' : ''} with form fields</li>
                  </ul>
                  <p className="mt-3">
                    When using this template, you'll be able to fill in all these fields through a user-friendly form interface.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No analysis data available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentTemplatePreview;
