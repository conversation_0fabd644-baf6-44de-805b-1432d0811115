import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { FaSave, FaDownload, FaSpinner } from 'react-icons/fa';
import TemplateService from '../services/templateService';

interface TemplateFormEditorProps {
  template: DocumentTemplate;
  customerId?: number;
  onSave: (blob: Blob | null, fileName: string, document?: any) => void;
  onCancel: () => void;
  testMode?: boolean;
}

interface FormField {
  name: string;
  label: string;
  value: string;
  type: 'text' | 'checkbox' | 'date' | 'textarea' | 'email' | 'tel' | 'number';
}

const TemplateFormEditor: React.FC<TemplateFormEditorProps> = ({
  template,
  customerId,
  onSave,
  onCancel,
  testMode = false
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [fields, setFields] = useState<FormField[]>([]);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [generating, setGenerating] = useState<boolean>(false);

  useEffect(() => {
    loadTemplate();
  }, [template.id]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the enhanced backend analysis
      const analysis = await TemplateService.analyzeTemplateFromBackend(template.id);

      // Create form fields from analysis
      const formFields: FormField[] = [
        // Regular fields
        ...analysis.fields.map(field => ({
          name: field.name,
          label: field.label,
          value: '',
          type: field.type as FormField['type']
        })),
        // Checkbox fields
        ...analysis.checkboxes.map(field => ({
          name: field.name,
          label: field.label,
          value: 'false',
          type: 'checkbox' as const
        })),
        // Table fields
        ...analysis.tables.flatMap(table =>
          table.fields.map(field => ({
            name: field.name,
            label: `${field.label} (Table ${table.index + 1})`,
            value: '',
            type: field.type as FormField['type']
          }))
        )
      ];

      setFields(formFields);

      // Also load the template content for filling
      const content = await TemplateService.loadTemplate(template.id);
      setTemplateContent(content);
    } catch (err: any) {
      setError(`Failed to load template: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (index: number, value: string) => {
    const updatedFields = [...fields];
    updatedFields[index].value = value;
    setFields(updatedFields);
  };

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const updatedFields = [...fields];
    updatedFields[index].value = checked ? 'true' : 'false';
    setFields(updatedFields);
  };

  const createTemplateData = () => {
    const data: Record<string, any> = {};

    fields.forEach(field => {
      if (field.type === 'checkbox') {
        // For checkboxes, we'll use true/false values
        data[field.name] = field.value === 'true';
      } else {
        data[field.name] = field.value;
      }
    });

    return data;
  };

  const handleSave = async () => {
    if (!customerId) {
      setError('Customer ID is required');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Use the backend service to generate the document
      const document = await TemplateService.generateDocumentFromTemplate(
        template.id,
        customerId,
        data
      );

      // Call the onSave callback with the document info
      onSave(null, document.name, document);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleSaveLocal = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Fill the template with the data (local processing)
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to generate document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!templateContent) return;

    try {
      setGenerating(true);
      setError(null);

      // Create template data from form fields
      const data = createTemplateData();

      // Fill the template with the data
      const docxBlob = await TemplateService.fillTemplate(templateContent, data);

      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;

      // Download the document
      TemplateService.downloadDocument(docxBlob, fileName);
    } catch (err: any) {
      setError(`Failed to download document: ${err.message || 'Unknown error'}`);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-amspm-primary text-2xl" />
        <span className="ml-2">Loading template...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-amspm-text">{template.name}</h3>
        <div className="flex space-x-2">
          <button
            onClick={onCancel}
            className="btn btn-outline btn-sm"
            disabled={generating}
          >
            Cancel
          </button>
          <button
            onClick={handleDownload}
            className="btn btn-outline btn-sm"
            disabled={generating}
            title="Download Document (Local Processing)"
          >
            <FaDownload className="mr-1" /> Download
          </button>
          {customerId ? (
            <button
              onClick={handleSave}
              className="btn btn-primary btn-sm"
              disabled={generating}
              title="Save to Customer Documents (Backend Processing)"
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-1" /> Saving</>
              ) : (
                <><FaSave className="mr-1" /> Save to Customer</>
              )}
            </button>
          ) : (
            <button
              onClick={handleSaveLocal}
              className="btn btn-primary btn-sm"
              disabled={generating}
              title="Generate Document (Local Processing)"
            >
              {generating ? (
                <><FaSpinner className="animate-spin mr-1" /> {testMode ? 'Testing' : 'Generating'}</>
              ) : (
                <><FaSave className="mr-1" /> {testMode ? 'Test' : 'Generate'}</>
              )}
            </button>
          )}
        </div>
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4 bg-white dark:bg-dark-input overflow-y-auto">
        {fields.length === 0 ? (
          <p className="text-gray-500 dark:text-dark-text-light italic">
            No form fields found in this template. Make sure your template contains fields in the format {'{field_name}'}.
          </p>
        ) : (
          <div className="space-y-4">
            {fields.map((field, index) => (
              <div key={index} className="form-control">
                <label className="label">
                  <span className="label-text text-amspm-text dark:text-dark-text">
                    {field.label}
                  </span>
                </label>

                {field.type === 'checkbox' ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      className="checkbox checkbox-primary"
                      checked={field.value === 'true'}
                      onChange={(e) => handleCheckboxChange(index, e.target.checked)}
                      disabled={generating}
                    />
                    <span className="ml-2 text-sm text-gray-500 dark:text-dark-text-light">
                      {field.value === 'true' ? 'Ja' : 'Nee'}
                    </span>
                  </div>
                ) : field.type === 'date' ? (
                  <input
                    type="date"
                    className="input input-bordered w-full"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                  />
                ) : field.type === 'textarea' ? (
                  <textarea
                    className="textarea textarea-bordered w-full"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                    rows={3}
                  />
                ) : (
                  <input
                    type={field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : field.type === 'number' ? 'number' : 'text'}
                    className="input input-bordered w-full"
                    value={field.value}
                    onChange={(e) => handleFieldChange(index, e.target.value)}
                    disabled={generating}
                    placeholder={`Vul ${field.label.toLowerCase()} in`}
                  />
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="text-sm text-gray-500 dark:text-dark-text-light">
        <p>Fill in the form fields and click {testMode ? 'Test' : 'Save'} when you're done.</p>
      </div>
    </div>
  );
};

export default TemplateFormEditor;
