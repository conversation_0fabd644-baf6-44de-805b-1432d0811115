# Document Template System - Complete Implementation

## Overview

This document describes the complete document template system that allows you to:

1. **Upload DOCX templates** with form fields and checkboxes
2. **Automatically analyze templates** to extract field information
3. **Generate dynamic forms** based on template structure
4. **Fill templates with customer data** and form input
5. **Save generated documents** directly to customer records

## System Architecture

### Backend Components

#### 1. Enhanced DocumentTemplateService (`backend/app/services/document_template_service.py`)

**New Methods:**
- `analyze_template_fields(template_id)` - Analyzes DOCX files to extract field information
- `generate_document_from_template(template_id, customer_id, form_data, user_id)` - Creates documents from templates
- `_extract_fields_from_docx(docx_content)` - Advanced DOCX parsing with XML analysis
- `_merge_customer_and_form_data(customer, form_data)` - Smart customer data integration
- `_fill_docx_template(template_content, data)` - Template filling with conditional logic

**Features:**
- **Smart Field Detection**: Automatically detects field types (text, email, phone, date, number, textarea)
- **Checkbox Support**: Handles conditional fields with `{#field_name}...{/field_name}` syntax
- **Table Processing**: Extracts fields from tables with position tracking
- **Customer Data Integration**: Auto-maps customer data to common field names
- **XML-based Processing**: Direct XML manipulation for reliable template filling

#### 2. New API Endpoints (`backend/app/controllers/document_template_controller.py`)

- `GET /document-templates/{id}/analyze` - Analyze template fields
- `POST /document-templates/{id}/generate` - Generate document from template

### Frontend Components

#### 1. Enhanced TemplateService (`frontend/src/services/templateService.ts`)

**New Methods:**
- `analyzeTemplateFromBackend(templateId)` - Uses backend analysis for better accuracy
- `generateDocumentFromTemplate(templateId, customerId, formData)` - Backend document generation

#### 2. Enhanced TemplateFormEditor (`frontend/src/components/TemplateFormEditor.tsx`)

**New Features:**
- **Backend Analysis Integration**: Uses server-side template analysis
- **Enhanced Field Types**: Supports text, email, tel, number, date, textarea, checkbox
- **Customer Integration**: Auto-populates customer data when customerId provided
- **Dual Processing Modes**: Backend processing (saves to customer) or local processing (download)

#### 3. New DocumentTemplatePreview (`frontend/src/components/DocumentTemplatePreview.tsx`)

**Features:**
- **Visual Template Analysis**: Shows all detected fields, checkboxes, and tables
- **Field Type Indicators**: Color-coded icons for different field types
- **Table Structure Display**: Shows field positions within tables
- **Template Summary**: Overview of template complexity

#### 4. Enhanced DocumentTemplateManager (`frontend/src/components/DocumentTemplateManager.tsx`)

**New Features:**
- **Preview Button**: Analyze templates before use
- **Enhanced Actions**: Preview, download, and delete templates

## Template Format Guide

### Supported Field Types

#### 1. Text Fields
```
{field_name} - Basic text input
{klantnaam} - Customer name
{adres} - Address
{projectnummer} - Project number (detected as number type)
{email} - Email address (detected as email type)
{telefoon} - Phone number (detected as tel type)
{datum} - Date (detected as date type)
{opmerking} - Comments (detected as textarea type)
```

#### 2. Checkbox Fields (Conditional)
```
{#installatie}This text appears if installatie is checked{/installatie}
{#if onderhoud}This text appears if onderhoud is checked{/if}
```

#### 3. Table Fields
Fields can be placed in table cells and will be detected with their position:
```
| Item | Value |
|------|-------|
| Project | {projectnummer} |
| Amount | {bedrag} |
```

### Customer Data Auto-Mapping

The system automatically maps customer data to common field names:

| Template Field | Customer Data |
|----------------|---------------|
| `{klantnaam}` | company_name or name |
| `{naam}` | name |
| `{bedrijfsnaam}` | company_name |
| `{adres}` | address |
| `{postcode}` | postal_code |
| `{plaats}` | city |
| `{telefoon}` | phone |
| `{email}` | email |
| `{klantnummer}` | customer ID |
| `{datum}` | Current date |

## Usage Instructions

### 1. Upload a Template

1. Go to **Document Templates** page
2. Click **Add Template**
3. Fill in template details:
   - **Name**: Descriptive name for the template
   - **Document Type**: Select appropriate document type
   - **Description**: Optional description
   - **File**: Upload your DOCX file with template fields
4. Click **Create Template**

### 2. Preview Template

1. In the templates list, click the **eye icon** (👁️) next to any template
2. The preview shows:
   - All detected text fields with their types
   - All detected checkboxes
   - Tables containing form fields
   - Summary of template complexity

### 3. Use Template for Customer

1. Go to **Customer Documents** page for any customer
2. Click **Upload Document**
3. Select **Use Template** option
4. Choose your template from the list
5. Fill in the generated form:
   - Customer data is auto-populated
   - Fill in remaining fields
   - Check/uncheck boxes as needed
6. Click **Save to Customer** to create the document

### 4. Test Mode

- Enable **Test Mode** to download the filled document without saving
- Useful for verifying template format before final upload
- Generated document can be reviewed and template adjusted if needed

## Technical Implementation Details

### Field Type Detection

The system uses intelligent field name analysis to determine appropriate input types:

```python
def _determine_field_type(self, field_name: str) -> str:
    field_lower = field_name.lower()
    
    if any(keyword in field_lower for keyword in ['datum', 'date']):
        return 'date'
    elif any(keyword in field_lower for keyword in ['email', 'e-mail']):
        return 'email'
    elif any(keyword in field_lower for keyword in ['telefoon', 'phone', 'tel']):
        return 'tel'
    elif any(keyword in field_lower for keyword in ['nummer', 'number', 'bedrag', 'prijs']):
        return 'number'
    elif any(keyword in field_lower for keyword in ['opmerking', 'beschrijving', 'notitie']):
        return 'textarea'
    else:
        return 'text'
```

### Template Processing Pipeline

1. **Upload**: Template uploaded to Firebase Storage
2. **Analysis**: DOCX file parsed to extract field information
3. **Form Generation**: Dynamic form created based on field analysis
4. **Data Merging**: Customer data merged with form input
5. **Template Filling**: DOCX template filled using XML manipulation
6. **Document Creation**: Filled document saved to customer records

### Error Handling

- **Template Validation**: Ensures DOCX files are valid
- **Field Extraction Fallback**: Uses regex fallback if XML parsing fails
- **Customer Data Safety**: Graceful handling of missing customer data
- **File Processing**: Proper cleanup of temporary files

## Benefits

1. **User-Friendly**: No technical knowledge required to create templates
2. **Automatic**: Customer data automatically populated
3. **Flexible**: Supports various field types and conditional content
4. **Reliable**: Backend processing ensures consistent results
5. **Integrated**: Documents saved directly to customer records
6. **Testable**: Test mode allows verification before final upload

## Example Template

Your "onderhoudsbon" template is perfect for this system. It likely contains fields like:
- `{klantnaam}` - Customer name
- `{adres}` - Address  
- `{telefoon}` - Phone number
- `{datum}` - Date
- Checkboxes for different service types
- Table fields for equipment details

The system will automatically detect all these fields and create an appropriate form interface for filling them in.
