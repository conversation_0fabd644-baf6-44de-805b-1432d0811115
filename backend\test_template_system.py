#!/usr/bin/env python3
"""
Test script for the document template system.
This script tests the enhanced template analysis and document generation functionality.
"""

import os
import sys
import tempfile
from io import BytesIO

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def create_test_docx():
    """Create a test DOCX file with template fields."""
    from docx import Document
    
    # Create a new document
    doc = Document()
    
    # Add title
    title = doc.add_heading('Test Template Document', 0)
    
    # Add some paragraphs with template fields
    doc.add_paragraph('Customer Information:')
    doc.add_paragraph('Name: {klantnaam}')
    doc.add_paragraph('Address: {adres}')
    doc.add_paragraph('Phone: {telefoon}')
    doc.add_paragraph('Email: {email}')
    doc.add_paragraph('Date: {datum}')
    
    # Add conditional fields (checkboxes)
    doc.add_paragraph('Services:')
    doc.add_paragraph('{#installatie}Installation Service{/installatie}')
    doc.add_paragraph('{#onderhoud}Maintenance Service{/onderhoud}')
    doc.add_paragraph('{#reparatie}Repair Service{/reparatie}')
    
    # Add a table with fields
    table = doc.add_table(rows=3, cols=2)
    table.style = 'Table Grid'
    
    # Header row
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Item'
    hdr_cells[1].text = 'Value'
    
    # Data rows
    row1_cells = table.rows[1].cells
    row1_cells[0].text = 'Project Number'
    row1_cells[1].text = '{projectnummer}'
    
    row2_cells = table.rows[2].cells
    row2_cells[0].text = 'Amount'
    row2_cells[1].text = '{bedrag}'
    
    # Save to BytesIO
    doc_buffer = BytesIO()
    doc.save(doc_buffer)
    doc_buffer.seek(0)
    
    return doc_buffer.getvalue()

def test_template_analysis():
    """Test the template analysis functionality."""
    print("Testing template analysis...")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        # Create test DOCX content
        docx_content = create_test_docx()
        
        # Create service instance
        service = DocumentTemplateService()
        
        # Test field extraction
        fields_info = service._extract_fields_from_docx(docx_content)
        
        print(f"✓ Found {len(fields_info['fields'])} text fields:")
        for field in fields_info['fields']:
            print(f"  - {field['name']} ({field['type']}) - {field['label']}")
        
        print(f"✓ Found {len(fields_info['checkboxes'])} checkboxes:")
        for checkbox in fields_info['checkboxes']:
            print(f"  - {checkbox['name']} - {checkbox['label']}")
        
        print(f"✓ Found {len(fields_info['tables'])} tables with fields:")
        for table in fields_info['tables']:
            print(f"  - Table {table['index']} with {len(table['fields'])} fields")
            for field in table['fields']:
                print(f"    - {field['name']} at row {field['row']}, cell {field['cell']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Template analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_template_filling():
    """Test the template filling functionality."""
    print("\nTesting template filling...")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        # Create test DOCX content
        docx_content = create_test_docx()
        
        # Create service instance
        service = DocumentTemplateService()
        
        # Test data
        test_data = {
            'klantnaam': 'Test Customer B.V.',
            'adres': 'Teststraat 123, 1234 AB Amsterdam',
            'telefoon': '020-1234567',
            'email': '<EMAIL>',
            'datum': '2024-01-15',
            'installatie': True,
            'onderhoud': False,
            'reparatie': True,
            'projectnummer': 'PRJ-2024-001',
            'bedrag': '€ 1,250.00'
        }
        
        # Fill the template
        filled_content = service._fill_docx_template(docx_content, test_data)
        
        # Save to temporary file for verification
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(filled_content)
            temp_file_path = temp_file.name
        
        print(f"✓ Template filled successfully")
        print(f"✓ Filled document saved to: {temp_file_path}")
        print("✓ You can open this file to verify the template was filled correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Template filling failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_data_merging():
    """Test customer data merging functionality."""
    print("\nTesting customer data merging...")
    
    try:
        from app.services.document_template_service import DocumentTemplateService
        
        # Create service instance
        service = DocumentTemplateService()
        
        # Mock customer object
        class MockCustomer:
            def __init__(self):
                self.id = 123
                self.name = "John Doe"
                self.company_name = "Test Company B.V."
                self.address = "Hoofdstraat 456"
                self.postal_code = "5678 CD"
                self.city = "Rotterdam"
                self.phone = "010-9876543"
                self.email = "<EMAIL>"
        
        customer = MockCustomer()
        
        # Form data
        form_data = {
            'projectnummer': 'PRJ-2024-002',
            'bedrag': '€ 2,500.00',
            'installatie': True,
            'onderhoud': True
        }
        
        # Merge data
        merged_data = service._merge_customer_and_form_data(customer, form_data)
        
        print("✓ Customer data merged successfully:")
        for key, value in merged_data.items():
            print(f"  - {key}: {value}")
        
        # Verify expected mappings
        expected_mappings = {
            'klantnaam': customer.company_name,
            'naam': customer.name,
            'adres': customer.address,
            'telefoon': customer.phone,
            'email': customer.email
        }
        
        for key, expected_value in expected_mappings.items():
            if key in merged_data and merged_data[key] == expected_value:
                print(f"✓ {key} mapped correctly")
            else:
                print(f"✗ {key} mapping failed")
        
        return True
        
    except Exception as e:
        print(f"✗ Customer data merging failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("DOCUMENT TEMPLATE SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        test_template_analysis,
        test_template_filling,
        test_customer_data_merging
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The document template system is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit(main())
