import <PERSON>z<PERSON><PERSON> from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { saveAs } from 'file-saver';
import { DocumentTemplate } from '../types/document_template';
import { getTemplateFileUrl } from './documentTemplateService';
import api from '../api';

/**
 * Service for handling document templates using Docxtemplater
 */
export class TemplateService {
  /**
   * Loads a template from a URL and returns the template as an ArrayBuffer
   * @param templateId ID of the template to load
   * @returns Promise resolving to the template ArrayBuffer
   */
  static async loadTemplate(templateId: number): Promise<ArrayBuffer> {
    try {
      const response = await fetch(getTemplateFileUrl(templateId));
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.error('Error loading template:', error);
      throw error;
    }
  }

  /**
   * Fills a template with data and returns the filled document as a blob
   * @param templateContent Template content as ArrayBuffer
   * @param data Data to fill the template with
   * @returns Promise resolving to the filled document blob
   */
  static async fillTemplate(templateContent: ArrayBuffer, data: Record<string, any>): Promise<Blob> {
    try {
      // Load the template
      const zip = new PizZip(templateContent);

      // Create a new instance of Docxtemplater with the recommended approach
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true
      });

      // Set the data to be injected
      doc.setData(data);

      // Render the document (replace all variables with their values)
      doc.render();

      // Get the zip document containing the filled template
      const out = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });

      return out;
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error filling template:', error.message);
        // If it's a rendering error, provide more details
        if (error.message.includes('rendering')) {
          console.error('Context:', error);
        }
      } else {
        console.error('Unknown error filling template:', error);
      }
      throw error;
    }
  }

  /**
   * Downloads a filled template
   * @param blob Blob containing the filled template
   * @param fileName Name of the file to download
   */
  static downloadDocument(blob: Blob, fileName: string): void {
    saveAs(blob, fileName);
  }

  /**
   * Analyzes a template to extract form fields
   * @param templateContent Template content as ArrayBuffer
   * @returns Promise resolving to an object with field information
   */
  static async analyzeTemplate(templateContent: ArrayBuffer): Promise<{
    fields: Array<{name: string, label: string}>;
    checkboxes: Array<{name: string, label: string}>;
  }> {
    try {
      // Load the template
      const zip = new PizZip(templateContent);

      // Get the main document content
      const content = zip.files['word/document.xml']?.asText() || '';

      // Extract fields using regex
      const fieldsMap = new Map<string, string>();
      const checkboxesMap = new Map<string, string>();

      // Look for simple fields in the format {field_name}
      const simpleFieldRegex = /{([^{}#/]+)}/g;
      let match;

      while ((match = simpleFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it's part of a conditional
        if (!content.includes(`{#if ${fieldName}}`) && !fieldName.includes('if') && !fieldName.includes('else')) {
          fieldsMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Look for conditional fields (checkboxes) in the format {#field_name} or {#if field_name}
      const conditionalFieldRegex = /{#(?:if\s+)?([^{}]+)}/g;

      while ((match = conditionalFieldRegex.exec(content)) !== null) {
        const fieldName = match[1].trim();
        // Skip if it contains "else" or other template keywords
        if (!fieldName.includes('else') && !fieldName.includes('/') && !fieldName.includes('^')) {
          checkboxesMap.set(fieldName, this.formatFieldLabel(fieldName));
        }
      }

      // Convert maps to arrays
      const fields = Array.from(fieldsMap).map(([name, label]) => ({ name, label }));
      const checkboxes = Array.from(checkboxesMap).map(([name, label]) => ({ name, label }));

      return { fields, checkboxes };
    } catch (error) {
      console.error('Error analyzing template:', error);
      throw error;
    }
  }

  /**
   * Formats a field name into a human-readable label
   * @param fieldName The raw field name from the template
   * @returns A formatted label
   */
  static formatFieldLabel(fieldName: string): string {
    // Remove prefixes like "check_"
    let label = fieldName.replace(/^check_/, '');

    // Replace underscores with spaces
    label = label.replace(/_/g, ' ');

    // Capitalize first letter of each word
    label = label.replace(/\b\w/g, c => c.toUpperCase());

    return label;
  }

  /**
   * Analyzes a template using the backend service to extract field information
   * @param templateId Template ID to analyze
   * @returns Promise resolving to template analysis
   */
  static async analyzeTemplateFromBackend(templateId: number): Promise<{
    template_id: number;
    template_name: string;
    document_type: string;
    fields: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
    }>;
    checkboxes: Array<{
      name: string;
      label: string;
      type: string;
      required: boolean;
    }>;
    tables: Array<{
      index: number;
      has_fields: boolean;
      fields: Array<{
        name: string;
        label: string;
        row: number;
        cell: number;
        type: string;
      }>;
      structure: string;
    }>;
  }> {
    try {
      const response = await api.get(`/document-templates/${templateId}/analyze`);
      return response.data;
    } catch (error) {
      console.error('Error analyzing template:', error);
      throw error;
    }
  }

  /**
   * Generates a document from a template with form data using the backend service
   * @param templateId Template ID to use
   * @param customerId Customer ID
   * @param formData Form data to fill the template
   * @returns Promise resolving to the created document
   */
  static async generateDocumentFromTemplate(
    templateId: number,
    customerId: number,
    formData: Record<string, any>
  ): Promise<any> {
    try {
      const response = await api.post(`/document-templates/${templateId}/generate`, {
        customer_id: customerId,
        form_data: formData
      });
      return response.data;
    } catch (error) {
      console.error('Error generating document from template:', error);
      throw error;
    }
  }
}

export default TemplateService;
