// frontend/src/pages/Customers.tsx
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getAllCustomers, createCustomer, updateCustomer, deleteCustomer, searchCustomers, bulkDeleteCustomers, deleteAllCustomers, getCustomerLinkedDataCount } from "../services/customerService";
import { Customer } from "../types/customer";
import LoadingSpinner from '../components/LoadingSpinner';
import CustomerModal from '../components/CustomerModal';
import Pagination from '../components/Pagination';
import ExportImportCustomers from '../components/ExportImportCustomers';
import { useConfirmation } from '../context/ConfirmationContext';
import { useAuth } from '../context/AuthContext';
import { FaPlus, FaFileExport, FaTrash } from "react-icons/fa";
import { MobileContainer, MobileCard, MobileButtonGroup, MobileSearch, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const Customers: React.FC = () => {
  const { showConfirmation } = useConfirmation();
  const { user } = useAuth();
  const { isMobile } = useMobile();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [newCustomer, setNewCustomer] = useState<Partial<Customer>>({});
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [showExportImport, setShowExportImport] = useState(false);
  const [selectedCustomers, setSelectedCustomers] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  const fetchCustomers = async (page: number, perPage: number) => {
    try {
      const response = await getAllCustomers(page, perPage);
      setCustomers(response.customers);
      setTotalItems(response.total);
      setSelectedCustomers(new Set()); // Clear selection when fetching new customers
      setLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch customers");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTerm.trim() === '') {
      fetchCustomers(currentPage, itemsPerPage);
    } else if (searchTerm.trim().length >= 2) {
      // Perform search when search term is at least 2 characters
      const performSearch = async () => {
        setIsSearching(true);
        try {
          const response = await searchCustomers(searchTerm);
          setCustomers(response.customers);
          setSelectedCustomers(new Set()); // Clear selection when searching
          // When searching, we don't have pagination info
          setTotalItems(response.customers.length);
        } catch (err: any) {
          setError(err.response?.data?.error || "Failed to search customers");
        } finally {
          setIsSearching(false);
        }
      };

      // Debounce the search to avoid too many API calls
      const timeoutId = setTimeout(() => {
        performSearch();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [currentPage, itemsPerPage, searchTerm]);

  // Update selectAll state based on selected customers
  useEffect(() => {
    if (customers.length === 0) {
      setSelectAll(false);
    } else {
      setSelectAll(selectedCustomers.size === customers.length);
    }
  }, [selectedCustomers, customers]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const customer = await createCustomer(newCustomer);
      setCustomers([...customers, customer]);
      setNewCustomer({});
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to create customer");
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCustomer) return;
    setSubmitting(true);
    try {
      const customer = await updateCustomer(editingCustomer.id, newCustomer);
      setCustomers(customers.map((c) => (c.id === editingCustomer.id ? customer : c)));
      setEditingCustomer(null);
      setNewCustomer({});
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to update customer");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    try {
      // Fetch linked data counts
      const counts = await getCustomerLinkedDataCount(customerId);

      const hasLinkedData = counts.total_items > 0;
      const linkedDataDetails = hasLinkedData ? `

Linked data that will be deleted:
• ${counts.documents} document(s)
• ${counts.sub_documents} sub-document(s)
• ${counts.events} event(s)
• ${counts.notes} note(s)
• ${counts.quotations} quotation(s)

Total items to be deleted: ${counts.total_items}` : `

No linked data found for this customer.`;

      showConfirmation({
        title: "Delete Customer",
        message: `⚠️ WARNING: You are about to permanently delete ${customer.name} and ALL associated data.${linkedDataDetails}

This action cannot be undone and may take a moment to complete if there are many associated records.

Are you sure you want to proceed?`,
        confirmText: "Yes, Delete Everything",
        cancelText: "Cancel",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: async () => {
          setSubmitting(true);
          try {
            await deleteCustomer(customerId);
            setCustomers(customers.filter((c) => c.id !== customerId));
            setSelectedCustomers(prev => {
              const newSet = new Set(prev);
              newSet.delete(customerId);
              return newSet;
            });
            setError(null);
          } catch (err: any) {
            console.error('Error deleting customer:', err);
            setError(err.response?.data?.error || "Failed to delete customer. Please make sure all associated data can be removed.");
          } finally {
            setSubmitting(false);
          }
        }
      });
    } catch (err: any) {
      // If we can't get counts, show a generic warning
      showConfirmation({
        title: "Delete Customer",
        message: `⚠️ WARNING: You are about to permanently delete ${customer.name} and ALL associated data.

This will delete:
• All documents uploaded for this customer
• All events assigned to this customer
• All customer notes
• All quotations for this customer
• Any sub-documents linked to main documents

This action cannot be undone and may take a moment to complete if there are many associated records.

Are you sure you want to proceed?`,
        confirmText: "Yes, Delete Everything",
        cancelText: "Cancel",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: async () => {
          setSubmitting(true);
          try {
            await deleteCustomer(customerId);
            setCustomers(customers.filter((c) => c.id !== customerId));
            setSelectedCustomers(prev => {
              const newSet = new Set(prev);
              newSet.delete(customerId);
              return newSet;
            });
            setError(null);
          } catch (err: any) {
            console.error('Error deleting customer:', err);
            setError(err.response?.data?.error || "Failed to delete customer. Please make sure all associated data can be removed.");
          } finally {
            setSubmitting(false);
          }
        }
      });
    }
  };

  const handleSelectCustomer = (customerId: number) => {
    setSelectedCustomers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(customerId)) {
        newSet.delete(customerId);
      } else {
        newSet.add(customerId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedCustomers.size === customers.length) {
      setSelectedCustomers(new Set());
    } else {
      setSelectedCustomers(new Set(customers.map(c => c.id)));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedCustomers.size === 0) return;

    const selectedCustomerNames = customers
      .filter(c => selectedCustomers.has(c.id))
      .map(c => c.name)
      .slice(0, 5); // Show first 5 names

    const namesList = selectedCustomerNames.join(', ');
    const additionalCount = selectedCustomers.size - selectedCustomerNames.length;
    const displayNames = additionalCount > 0 ? `${namesList} and ${additionalCount} more` : namesList;

    showConfirmation({
      title: "Delete Selected Customers",
      message: `⚠️ WARNING: You are about to permanently delete ${selectedCustomers.size} customer(s) and ALL their associated data.

Selected customers: ${displayNames}

This will delete for each customer:
• All documents uploaded for the customer
• All events assigned to the customer
• All customer notes
• All quotations for the customer
• Any sub-documents linked to main documents

This action cannot be undone and may take several minutes to complete if there are many associated records.

Are you sure you want to proceed?`,
      confirmText: "Yes, Delete All Selected",
      cancelText: "Cancel",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          const result = await bulkDeleteCustomers(Array.from(selectedCustomers));
          setCustomers(customers.filter(c => !selectedCustomers.has(c.id)));
          setSelectedCustomers(new Set());
          setSelectAll(false);
          setError(null);

          if (result.failed > 0) {
            setError(`Successfully deleted ${result.deleted} customers. Failed to delete ${result.failed} customers.`);
          }
        } catch (err: any) {
          console.error('Error bulk deleting customers:', err);
          setError(err.response?.data?.error || "Failed to delete customers. Please try again.");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  const handleDeleteAllCustomers = async () => {
    showConfirmation({
      title: "Delete ALL Customers",
      message: `🚨 EXTREME DANGER: This will permanently delete ALL customers in the entire database and ALL their associated data.

This action will delete:
• ALL customer records in the system
• ALL documents for every customer
• ALL events for every customer
• ALL customer notes
• ALL quotations for every customer
• ALL sub-documents linked to main documents

⚠️ This affects ALL customers in the database, not just the ones visible on this page.
⚠️ This operation cannot be undone and may take several minutes to complete.
⚠️ This will essentially reset your entire customer database.

Are you absolutely certain you want to proceed with this destructive action?`,
      confirmText: "Yes, Delete EVERYTHING",
      cancelText: "Cancel",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          const result = await deleteAllCustomers();
          setCustomers([]);
          setSelectedCustomers(new Set());
          setSelectAll(false);
          setTotalItems(0);
          setError(null);

          showConfirmation({
            title: "All Customers Deleted",
            message: `Successfully deleted ${result.deleted} customers. ${result.failed > 0 ? `Failed to delete ${result.failed} customers.` : ''}`,
            confirmText: "OK",
            confirmButtonClass: "bg-green-600 hover:bg-green-700",
            onConfirm: () => {}
          });
        } catch (err: any) {
          console.error('Error deleting all customers:', err);
          setError(err.response?.data?.error || "Failed to delete all customers. Please try again.");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  if (loading) {
    return <LoadingSpinner message="Loading customers..." />;
  }

  return (
    <MobileContainer>
      <MobilePageHeader
        title="Customer Management"
        actions={
          <MobileButtonGroup direction="responsive">
            {user?.role === 'administrator' && (
              <button
                onClick={() => setShowExportImport(!showExportImport)}
                className="btn btn-outline mobile-touch-target"
              >
                <FaFileExport className="mr-2" /> {showExportImport ? 'Hide Export/Import' : 'Export/Import'}
              </button>
            )}
            {selectedCustomers.size > 0 && user?.role === 'administrator' && (
              <button
                onClick={handleBulkDelete}
                className="btn btn-danger mobile-touch-target"
                disabled={submitting}
              >
                <FaTrash className="mr-2" /> Delete Selected ({selectedCustomers.size})
              </button>
            )}
            {user?.role === 'administrator' && (
              <button
                onClick={handleDeleteAllCustomers}
                className="btn bg-red-800 hover:bg-red-900 text-white mobile-touch-target"
                disabled={submitting}
                title="Delete ALL customers in the database (not just visible ones)"
              >
                <FaTrash className="mr-2" /> Delete ALL Customers
              </button>
            )}
            <button
              onClick={() => {
                setNewCustomer({});
                setEditingCustomer(null);
                setShowModal(true);
              }}
              className="btn btn-secondary mobile-touch-target"
            >
              <FaPlus className="mr-2" /> Create Customer
            </button>
          </MobileButtonGroup>
        }
      />

      {user?.role === 'administrator' && showExportImport && (
        <div className="mb-4 sm:mb-6">
          <ExportImportCustomers />
        </div>
      )}

      <MobileSearch
        value={searchTerm}
        onChange={setSearchTerm}
        placeholder="Search customers by name..."
        onClear={searchTerm.trim() !== '' ? () => {
          setSearchTerm('');
          fetchCustomers(currentPage, itemsPerPage);
        } : undefined}
        className="mb-4 sm:mb-6"
      />

      {searchTerm.trim().length === 1 && (
        <p className="text-xs sm:text-sm text-gray-500 dark:text-dark-text-light mb-4">
          Type at least 2 characters to search
        </p>
      )}

      {isSearching && (
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amspm-primary dark:border-dark-accent"></div>
        </div>
      )}

      {error && <p className="text-red-500 mb-4">{error}</p>}

      {showModal && (
        <CustomerModal
          customer={newCustomer}
          onClose={() => {
            setShowModal(false);
            setEditingCustomer(null);
            setNewCustomer({});
          }}
          onSubmit={editingCustomer ? handleUpdate : handleCreate}
          setCustomer={setNewCustomer}
          isEditing={!!editingCustomer}
          submitting={submitting}
        />
      )}

      {searchTerm.trim().length >= 2 && customers.length === 0 && !isSearching ? (
        <div className="bg-white dark:bg-dark-secondary rounded-lg shadow p-4 sm:p-8 text-center">
          <p className="text-sm sm:text-base text-gray-500 dark:text-dark-text-light">No customers found matching "{searchTerm}"</p>
        </div>
      ) : (
        <>
          {/* Desktop view */}
          <div className="hidden md:block bg-white dark:bg-dark-secondary rounded-lg shadow overflow-hidden overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr>
                  {user?.role === 'administrator' && (
                    <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-amspm-primary focus:ring-amspm-primary"
                      />
                    </th>
                  )}
                  <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs leading-4 font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs leading-4 font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Address
                  </th>
                  <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary"></th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-dark-border">
                {customers.map((customer) => (
                <tr key={customer.id} className="hover:bg-gray-50 dark:hover:bg-dark-hover">
                  {user?.role === 'administrator' && (
                    <td className="px-6 py-4 whitespace-no-wrap border-b border-gray-200 dark:border-dark-border">
                      <input
                        type="checkbox"
                        checked={selectedCustomers.has(customer.id)}
                        onChange={() => handleSelectCustomer(customer.id)}
                        className="rounded border-gray-300 text-amspm-primary focus:ring-amspm-primary"
                      />
                    </td>
                  )}
                  <td className="px-6 py-4 whitespace-no-wrap border-b border-gray-200 dark:border-dark-border text-amspm-text dark:text-dark-text">
                    {customer.name}
                  </td>
                  <td className="px-6 py-4 whitespace-no-wrap border-b border-gray-200 dark:border-dark-border text-amspm-text dark:text-dark-text">
                    {customer.address}
                  </td>
                  <td className="px-6 py-4 whitespace-no-wrap border-b border-gray-200 dark:border-dark-border text-right">
                    <div className="flex justify-end space-x-4">
                      <Link to={`/customers/${customer.id}`} className="btn btn-primary">
                        View Customer
                      </Link>
                      <Link to={`/customers/${customer.id}/documents`} className="btn btn-secondary">
                        Documents
                      </Link>
                      <button
                        onClick={() => {
                          setEditingCustomer(customer);
                          setNewCustomer({
                            code: customer.code,
                            name: customer.name,
                            kvk_number: customer.kvk_number,
                            contact_person: customer.contact_person,
                            gender: customer.gender,
                            title: customer.title,
                            address: customer.address,
                            postal_code: customer.postal_code,
                            city: customer.city,
                            country: customer.country,
                            address2: customer.address2,
                            postal_code2: customer.postal_code2,
                            city2: customer.city2,
                            country2: customer.country2,
                            phone: customer.phone,
                            mobile: customer.mobile,
                            fax: customer.fax,
                            email: customer.email,
                            invoice_email: customer.invoice_email,
                            reminder_email: customer.reminder_email,
                            website: customer.website,
                            bank_account: customer.bank_account,
                            giro_account: customer.giro_account,
                            vat_number: customer.vat_number,
                            iban: customer.iban,
                          });
                          setShowModal(true);
                        }}
                        className="btn btn-outline"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(customer.id)}
                        className="btn btn-danger"
                        disabled={submitting}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile view - Cards */}
        <div className="md:hidden space-y-4">
          {user?.role === 'administrator' && customers.length > 0 && (
            <div className="flex items-center justify-between bg-gray-50 dark:bg-dark-tertiary rounded-lg p-3">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-amspm-primary focus:ring-amspm-primary mr-2"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-dark-text">
                  Select All ({customers.length})
                </span>
              </label>
              {selectedCustomers.size > 0 && (
                <span className="text-sm text-amspm-primary dark:text-dark-accent font-medium">
                  {selectedCustomers.size} selected
                </span>
              )}
            </div>
          )}
          {customers.map((customer) => (
            <MobileCard key={customer.id}>
              <div className="mobile-card-header">
                <div className="flex-1">
                  <h3 className="mobile-card-title">{customer.name}</h3>
                  <p className="mobile-card-value text-sm mt-1">{customer.address}</p>
                  {customer.city && (
                    <p className="mobile-card-value text-sm">{customer.postal_code} {customer.city}</p>
                  )}
                </div>
                {user?.role === 'administrator' && (
                  <input
                    type="checkbox"
                    checked={selectedCustomers.has(customer.id)}
                    onChange={() => handleSelectCustomer(customer.id)}
                    className="rounded border-gray-300 text-amspm-primary focus:ring-amspm-primary mobile-touch-target"
                  />
                )}
              </div>

              {(customer.phone || customer.email) && (
                <div className="mobile-card-content">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    {customer.phone && (
                      <div className="mobile-card-row">
                        <span className="mobile-card-label">Phone:</span>
                        <span className="mobile-card-value">{customer.phone}</span>
                      </div>
                    )}
                    {customer.email && (
                      <div className="mobile-card-row">
                        <span className="mobile-card-label">Email:</span>
                        <span className="mobile-card-value truncate">{customer.email}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="mobile-card-actions">
                <MobileButtonGroup direction="horizontal">
                  <Link
                    to={`/customers/${customer.id}`}
                    className="btn btn-primary btn-sm mobile-touch-target"
                  >
                    View Customer
                  </Link>
                  <Link
                    to={`/customers/${customer.id}/documents`}
                    className="btn btn-secondary btn-sm mobile-touch-target"
                  >
                    Documents
                  </Link>
                </MobileButtonGroup>
                <MobileButtonGroup direction="horizontal">
                  <button
                    onClick={() => {
                      setEditingCustomer(customer);
                      setNewCustomer({
                        code: customer.code,
                        name: customer.name,
                        kvk_number: customer.kvk_number,
                        contact_person: customer.contact_person,
                        gender: customer.gender,
                        title: customer.title,
                        address: customer.address,
                        postal_code: customer.postal_code,
                        city: customer.city,
                        country: customer.country,
                        address2: customer.address2,
                        postal_code2: customer.postal_code2,
                        city2: customer.city2,
                        country2: customer.country2,
                        phone: customer.phone,
                        mobile: customer.mobile,
                        fax: customer.fax,
                        email: customer.email,
                        invoice_email: customer.invoice_email,
                        reminder_email: customer.reminder_email,
                        website: customer.website,
                        bank_account: customer.bank_account,
                        giro_account: customer.giro_account,
                        vat_number: customer.vat_number,
                        iban: customer.iban,
                      });
                      setShowModal(true);
                    }}
                    className="btn btn-outline btn-sm mobile-touch-target"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(customer.id)}
                    className="btn btn-danger btn-sm mobile-touch-target"
                    disabled={submitting}
                  >
                    Delete
                  </button>
                </MobileButtonGroup>
              </div>
            </MobileCard>
          ))}

          {customers.length === 0 && (
            <div className="bg-white dark:bg-dark-secondary rounded-lg shadow p-4 text-center text-gray-500 dark:text-dark-text-light">
              No customers found.
            </div>
          )}
        </div>
        </>
      )}
      {searchTerm.trim() === '' && (
        <div className="mt-6">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
            <div className="text-sm text-gray-600 dark:text-dark-text-light mb-2 sm:mb-0">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} customers
              {selectedCustomers.size > 0 && (
                <span className="ml-2 text-amspm-primary dark:text-dark-accent font-medium">
                  ({selectedCustomers.size} selected on this page)
                </span>
              )}
            </div>
            {user?.role === 'administrator' && totalItems > itemsPerPage && (
              <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 px-2 py-1 rounded">
                ⚠️ "Select All" only selects customers on this page. Use "Delete ALL Customers" to delete all {totalItems} customers.
              </div>
            )}
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalItems / itemsPerPage)}
            onPageChange={handlePageChange}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </MobileContainer>
  );
};

export default Customers;
