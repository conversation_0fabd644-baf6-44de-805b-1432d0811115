"""
Document Template controller module.
This module provides API endpoints for document templates.
"""
from flask import Blueprint, request, jsonify, send_file
from app.services.document_template_service import DocumentTemplateService
from app.utils.security import role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.file_validation import validate_file
from app.schemas.document_template_schema import document_template_schema
import logging
from marshmallow import ValidationError

# Define the blueprint for document template-related routes
document_template_bp = Blueprint("document_template", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the document template service
document_template_service = DocumentTemplateService()

@document_template_bp.route("", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_all_templates():
    """
    Get all document templates.

    Returns:
        JSON: List of document templates.
    """
    try:
        templates = document_template_service.get_all_templates()
        logger.info("Fetched all document templates")
        return jsonify(templates), 200
    except Exception as e:
        logger.error(f"Failed to fetch document templates: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_template(template_id):
    """
    Get a specific document template by ID.

    Path Parameters:
        template_id (int): The ID of the template to retrieve.

    Returns:
        JSON: Template details.
    """
    try:
        template = document_template_service.get_template_by_id(template_id)
        if not template:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404
        logger.info(f"Fetched template with ID {template_id}")
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to fetch template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/document-type/<string:document_type>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_templates_by_document_type(document_type):
    """
    Get all document templates for a specific document type.

    Path Parameters:
        document_type (str): The document type to filter by.

    Returns:
        JSON: List of templates for the specified document type.
    """
    try:
        templates = document_template_service.get_templates_by_document_type(document_type)
        logger.info(f"Fetched templates for document type {document_type}")
        return jsonify(templates), 200
    except Exception as e:
        logger.error(f"Failed to fetch templates for document type {document_type}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("", methods=["POST"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def create_template():
    """
    Create a new document template.

    Request Body: Form data with template details and file
    Returns: JSON: The created template
    """
    try:
        # Validate file
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]
        if not file.filename or file.filename.strip() == "":
            return jsonify({"error": "No file selected"}), 400

        # Validate file using the file validation utility
        is_valid, message = validate_file(file)
        if not is_valid:
            logger.warning(f"File validation failed: {message}")
            return jsonify({"error": message}), 400

        # Parse form data
        template_data = {
            "name": request.form.get("name"),
            "document_type": request.form.get("document_type"),
            "description": request.form.get("description"),
            "created_by": request.current_user.id
        }

        # Validate the input data using the schema
        errors = document_template_schema.validate(template_data)
        if errors:
            logger.warning(f"Template validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create the template
        template = document_template_service.create_template(template_data, file)
        logger.info(f"Created template: {template['name']}")
        return jsonify(template), 201
    except ValidationError as e:
        logger.warning(f"Template validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create template: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["PUT"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def update_template(template_id):
    """
    Update an existing document template.

    Path Parameters:
        template_id (int): The ID of the template to update.

    Request Body: JSON with template details
    Returns: JSON: The updated template
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        template = document_template_service.update_template(template_id, data)
        logger.info(f"Updated template with ID {template_id}")
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to update template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>", methods=["DELETE"], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def delete_template(template_id):
    """
    Delete a document template by ID.

    Path Parameters:
        template_id (int): The ID of the template to delete.

    Returns:
        JSON: Success message.
    """
    try:
        success = document_template_service.delete_template(template_id)
        if success:
            logger.info(f"Deleted template with ID {template_id}")
            return jsonify({"message": "Template deleted successfully"}), 200
        else:
            logger.warning(f"Failed to delete template with ID {template_id}")
            return jsonify({"error": "Failed to delete template"}), 500
    except Exception as e:
        logger.error(f"Failed to delete template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/analyze", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def analyze_template_fields(template_id):
    """
    Analyze a template to extract field information.

    Path Parameters:
        template_id (int): The ID of the template to analyze.

    Returns:
        JSON: Template field analysis including fields, checkboxes, and tables.
    """
    try:
        analysis = document_template_service.analyze_template_fields(template_id)
        logger.info(f"Analyzed template fields for template ID {template_id}")
        return jsonify(analysis), 200
    except ValueError as e:
        logger.warning(f"Template not found: {str(e)}")
        return jsonify({"error": str(e)}), 404
    except Exception as e:
        logger.error(f"Failed to analyze template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/generate", methods=["POST"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def generate_document_from_template(template_id):
    """
    Generate a document from a template with form data.

    Path Parameters:
        template_id (int): The ID of the template to use.

    Request Body: JSON with customer_id and form_data
    Returns: JSON: The created document
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        customer_id = data.get('customer_id')
        form_data = data.get('form_data', {})

        if not customer_id:
            return jsonify({"error": "customer_id is required"}), 400

        current_user = request.current_user
        document = document_template_service.generate_document_from_template(
            template_id, customer_id, form_data, current_user.id
        )

        logger.info(f"Generated document from template {template_id} for customer {customer_id}")
        return jsonify(document), 201
    except ValueError as e:
        logger.warning(f"Invalid request: {str(e)}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to generate document from template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_template_bp.route("/<int:template_id>/file", methods=["GET"], strict_slashes=False)
@rate_limit("60/minute")
def get_template_file(template_id):
    """
    Get the file for a specific document template.

    Path Parameters:
        template_id (int): The ID of the template to retrieve the file for.

    Returns:
        File: The template file.
    """
    try:
        # Allow file downloads without authentication
        # This is safe because we're only serving template files that should be accessible to all users
        return document_template_service.get_template_file(template_id)
    except Exception as e:
        logger.error(f"Failed to fetch template file for template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
