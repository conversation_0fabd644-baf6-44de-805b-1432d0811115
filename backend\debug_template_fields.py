#!/usr/bin/env python3
"""
Debug script to test template field extraction
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.document_template_service import DocumentTemplateService

def debug_template_fields():
    """Debug template field extraction for our new onderhoudsbon template"""
    app, _ = create_app()

    with app.app_context():
        try:
            service = DocumentTemplateService()

            # Test with our new template file
            print("Testing field extraction with new onderhoudsbon template...")

            # Load the template file directly
            template_path = 'sample_templates/onderhoudsbon_template.docx'
            with open(template_path, 'rb') as f:
                template_content = f.read()

            fields_info = service._extract_fields_from_docx(template_content)
            
            print(f"\n=== ONDERHOUDSBON TEMPLATE ===")
            print(f"Template file: {template_path}")
            
            print(f"\n=== TEXT FIELDS ({len(fields_info['fields'])}) ===")
            for i, field in enumerate(fields_info['fields'], 1):
                print(f"{i:2d}. Name: '{field['name']}'")
                print(f"    Label: '{field['label']}'")
                print(f"    Type: {field['type']}")
                print(f"    Required: {field['required']}")
                print()
            
            print(f"\n=== CHECKBOXES ({len(fields_info['checkboxes'])}) ===")
            for i, checkbox in enumerate(fields_info['checkboxes'], 1):
                print(f"{i:2d}. Name: '{checkbox['name']}'")
                print(f"    Label: '{checkbox['label']}'")
                print(f"    Type: {checkbox['type']}")
                print(f"    Required: {checkbox['required']}")
                print()
            
            print(f"\n=== TABLES ({len(fields_info['tables'])}) ===")
            for i, table in enumerate(fields_info['tables'], 1):
                print(f"{i:2d}. Table {table['index']} - Has Fields: {table['has_fields']}")
                if table['has_fields']:
                    for field in table['fields']:
                        print(f"    - {field['name']} (Row {field['row']}, Cell {field['cell']})")
                print()
            
            return True
            
        except Exception as e:
            print(f"Error analyzing template: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("=== TEMPLATE FIELD EXTRACTION DEBUG ===")
    success = debug_template_fields()
    
    if success:
        print("✓ Template analysis completed successfully")
    else:
        print("✗ Template analysis failed")
